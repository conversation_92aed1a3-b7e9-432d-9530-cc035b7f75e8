#!/usr/bin/env python3
"""
Simple test script for the Stock Data Collector API
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✅ Health check: {response.status_code} - {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_post_stocks():
    """Test posting stock symbols"""
    print("\n📊 Testing POST /stocks...")
    
    test_data = {
        "stocks": "AAPL,GOOGLE,TESLA,MSFT,AMZN"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/stocks",
            headers={"Content-Type": "application/json"},
            data=json.dumps(test_data)
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ POST stocks: {response.status_code}")
            print(f"📈 Created {len(data)} records:")
            for record in data:
                print(f"   - ID: {record['id']}, Symbol: {record['symbol']}, Time: {record['timestamp']}")
            return True
        else:
            print(f"❌ POST stocks failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ POST stocks error: {e}")
        return False

def test_get_stocks():
    """Test getting all stock records"""
    print("\n📋 Testing GET /stocks...")
    
    try:
        response = requests.get(f"{BASE_URL}/stocks")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ GET stocks: {response.status_code}")
            print(f"📊 Total records: {len(data)}")
            
            if data:
                print("📈 Recent records:")
                for record in data[-3:]:  # Show last 3 records
                    print(f"   - ID: {record['id']}, Symbol: {record['symbol']}, Time: {record['timestamp']}")
            return True
        else:
            print(f"❌ GET stocks failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ GET stocks error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting API tests...\n")
    
    # Wait a moment for the server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    tests = [
        test_health_check,
        test_post_stocks,
        test_get_stocks
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # Small delay between tests
    
    print(f"\n🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed. Check the server logs.")

if __name__ == "__main__":
    main()
