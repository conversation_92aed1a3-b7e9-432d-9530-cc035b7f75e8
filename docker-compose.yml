version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: stocksdb
      POSTGRES_USER: stockuser
      POSTGRES_PASSWORD: stockpass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U stockuser -d stocksdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Application
  fastapi-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************************/stocksdb
      - NGROK_AUTH_TOKEN=${NGROK_AUTH_TOKEN:-}
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  # Alternative: FastAPI with SQLite (comment out postgres and use this instead)
  # fastapi-app-sqlite:
  #   build: .
  #   ports:
  #     - "8000:8000"
  #   environment:
  #     - DATABASE_URL=sqlite:///./data/stocks.db
  #     - NGROK_AUTH_TOKEN=${NGROK_AUTH_TOKEN:-}
  #   volumes:
  #     - ./data:/app/data
  #   restart: unless-stopped

volumes:
  postgres_data:
