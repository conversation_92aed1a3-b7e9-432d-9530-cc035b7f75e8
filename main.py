from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from datetime import datetime
import os
from typing import List

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./stocks.db")
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Database model
class StockRecord(Base):
    __tablename__ = "stock_records"
    
    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    symbol = Column(String, index=True)

# Create tables
Base.metadata.create_all(bind=engine)

# Pydantic models
class StocksRequest(BaseModel):
    stocks: str

class StockResponse(BaseModel):
    id: int
    timestamp: datetime
    symbol: str
    
    class Config:
        from_attributes = True

# FastAPI app
app = FastAPI(
    title="Stock Data Collector",
    description="API to collect and store stock symbols",
    version="1.0.0"
)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/")
async def root():
    return {"message": "Stock Data Collector API", "status": "running"}

@app.post("/stocks", response_model=List[StockResponse])
async def create_stock_records(request: StocksRequest):
    """
    Create stock records from comma-separated stock symbols
    """
    try:
        # Parse the comma-separated stocks
        stock_symbols = [symbol.strip().upper() for symbol in request.stocks.split(",") if symbol.strip()]
        
        if not stock_symbols:
            raise HTTPException(status_code=400, detail="No valid stock symbols provided")
        
        # Get database session
        db = SessionLocal()
        created_records = []
        
        try:
            # Create records for each stock symbol
            for symbol in stock_symbols:
                stock_record = StockRecord(symbol=symbol)
                db.add(stock_record)
                db.flush()  # Flush to get the ID
                created_records.append(stock_record)
            
            db.commit()
            
            # Return the created records
            return [StockResponse.from_orm(record) for record in created_records]
            
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            db.close()
            
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing request: {str(e)}")

@app.get("/stocks", response_model=List[StockResponse])
async def get_all_stocks():
    """
    Get all stock records from the database
    """
    db = SessionLocal()
    try:
        stocks = db.query(StockRecord).all()
        return [StockResponse.from_orm(stock) for stock in stocks]
    finally:
        db.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
