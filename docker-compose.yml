version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: stocksdb
      POSTGRES_USER: stockuser
      POSTGRES_PASSWORD: stockpass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U stockuser -d stocksdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  ngrok:
      image: ngrok/ngrok:latest
      container_name: chartink_ngrok
      environment:
        - NGROK_AUTHTOKEN=${NGROK_AUTHTOKEN:-}
      ports:
        - "4040:4040"
      command: http host.docker.internal:8000
      restart: unless-stopped

volumes:
  postgres_data:
