# Stock Data Collector API

A FastAPI application that accepts stock symbols and stores them in a database with timestamps. The application is fully containerized with Docker and includes ngrok integration for public access.

## Features

- **FastAPI** web framework with automatic API documentation
- **POST endpoint** to accept comma-separated stock symbols
- **Database storage** with SQLAlchemy (supports SQLite and PostgreSQL)
- **Docker containerization** for easy deployment
- **ngrok integration** for public URL access
- **Automatic API documentation** at `/docs`

## API Endpoints

### POST /stocks
Accepts a JSON payload with comma-separated stock symbols and stores each symbol with a timestamp.

**Request Body:**
```json
{
    "stocks": "AAPL,GOOGLE,TESLA"
}
```

**Response:**
```json
[
    {
        "id": 1,
        "timestamp": "2024-01-15T10:30:00.123456",
        "symbol": "AAPL"
    },
    {
        "id": 2,
        "timestamp": "2024-01-15T10:30:00.123456",
        "symbol": "GOOGLE"
    },
    {
        "id": 3,
        "timestamp": "2024-01-15T10:30:00.123456",
        "symbol": "TESLA"
    }
]
```

### GET /stocks
Returns all stored stock records.

### GET /
Health check endpoint.

## Quick Start

### Option 1: Docker Compose (Recommended)

1. **Clone and navigate to the project:**
   ```bash
   cd chartink-data-collector
   ```

2. **Run with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

3. **Access the API:**
   - Local: http://localhost:8000
   - API Docs: http://localhost:8000/docs

### Option 2: Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application:**
   ```bash
   python main.py
   ```

3. **Access the API:**
   - Local: http://localhost:8000
   - API Docs: http://localhost:8000/docs

### Option 3: With ngrok (Public Access)

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set ngrok auth token (optional):**
   ```bash
   export NGROK_AUTH_TOKEN=your_ngrok_token
   ```

3. **Run with ngrok:**
   ```bash
   python ngrok_runner.py
   ```

## Testing the API

### Using curl:
```bash
# Test the POST endpoint
curl -X POST "http://localhost:8000/stocks" \
     -H "Content-Type: application/json" \
     -d '{"stocks": "AAPL,GOOGLE,TESLA,MSFT"}'

# Get all stocks
curl -X GET "http://localhost:8000/stocks"
```

### Using the interactive docs:
Visit `http://localhost:8000/docs` for the automatic interactive API documentation.

## Database

The application supports both SQLite (default) and PostgreSQL:

- **SQLite**: Data stored in `./data/stocks.db`
- **PostgreSQL**: Configure via `DATABASE_URL` environment variable

## Environment Variables

- `DATABASE_URL`: Database connection string (default: SQLite)
- `NGROK_AUTH_TOKEN`: ngrok authentication token (optional)

## Project Structure

```
chartink-data-collector/
├── main.py              # Main FastAPI application
├── database.py          # Database models and configuration
├── ngrok_runner.py      # ngrok integration script
├── requirements.txt     # Python dependencies
├── Dockerfile          # Docker configuration
├── docker-compose.yml  # Docker Compose configuration
└── README.md           # This file
```

## Development

To add new features or modify the application:

1. Edit the source code
2. Test locally: `python main.py`
3. Rebuild Docker image: `docker-compose up --build`

## License

This project is open source and available under the MIT License.
