import os
import subprocess
import time
import threading
from pyngrok import ngrok
import uvicorn
from main import app

def start_fastapi():
    """Start the FastAPI application"""
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

def start_ngrok():
    """Start ngrok tunnel"""
    # Set ngrok auth token if provided
    ngrok_token = os.getenv("NGROK_AUTH_TOKEN")
    if ngrok_token:
        ngrok.set_auth_token(ngrok_token)
    
    # Create tunnel
    public_url = ngrok.connect(8000)
    print(f"🌐 Public URL: {public_url}")
    print(f"📊 FastAPI Docs: {public_url}/docs")
    print(f"🔗 Test endpoint: {public_url}/stocks")
    
    return public_url

if __name__ == "__main__":
    print("🚀 Starting Stock Data Collector with ngrok...")
    
    # Start FastAPI in a separate thread
    fastapi_thread = threading.Thread(target=start_fastapi, daemon=True)
    fastapi_thread.start()
    
    # Wait a moment for FastAPI to start
    time.sleep(3)
    
    # Start ngrok tunnel
    try:
        public_url = start_ngrok()
        print("\n✅ Application is running!")
        print("Press Ctrl+C to stop...")
        
        # Keep the main thread alive
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        ngrok.disconnect(public_url)
        ngrok.kill()
    except Exception as e:
        print(f"❌ Error: {e}")
        ngrok.kill()
